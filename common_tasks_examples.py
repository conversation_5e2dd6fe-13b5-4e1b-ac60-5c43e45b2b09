#!/usr/bin/env python3
"""
常用网页自动化任务示例
"""

import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def run_task(session, task_description):
    """执行单个任务"""
    print(f"\n🔧 任务: {task_description}")
    result = await session.call_tool(
        "intelligent_web_task",
        {"task_description": task_description}
    )
    print(f"✅ 结果: {result.content[0].text}")

async def common_tasks_demo():
    """常用任务演示"""
    
    server_params = StdioServerParameters(
        command="python",
        args=["demo_mcp_server.py"],
        cwd="."
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            print("🎯 常用网页自动化任务演示")
            print("=" * 40)
            
            # 1. 网站导航
            await run_task(session, "打开github.com")
            await run_task(session, "点击Sign in按钮")
            
            # 2. 搜索操作
            await run_task(session, "打开google.com")
            await run_task(session, "搜索'Python web scraping'")
            
            # 3. 表单填写
            await run_task(session, "打开一个注册页面")
            await run_task(session, "在用户名输入框中输入'testuser'")
            await run_task(session, "在邮箱输入框中输入'<EMAIL>'")
            
            # 4. 页面信息提取
            await run_task(session, "获取当前页面的所有链接")
            await run_task(session, "获取页面标题和描述")
            await run_task(session, "提取页面中的所有图片链接")
            
            # 5. 页面交互
            await run_task(session, "滚动到页面底部")
            await run_task(session, "点击第一个搜索结果")
            await run_task(session, "返回上一页")
            
            # 6. 截图和状态
            print("\n📸 截图当前页面...")
            await session.call_tool("take_screenshot", {})
            
            print("\n📊 查看浏览器状态...")
            result = await session.call_tool("browser_status", {})
            print(f"状态: {result.content[0].text}")
            
            # 7. 清理
            print("\n🧹 清理资源...")
            await session.call_tool("cleanup_browser", {})
            
            print("\n🎉 演示完成！")

if __name__ == "__main__":
    asyncio.run(common_tasks_demo())
