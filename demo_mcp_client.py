#!/usr/bin/env python3
"""
iICrawlerMCP客户端演示
展示如何通过MCP协议与iICrawlerMCP服务器交互
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    from mcp.types import TextContent
except ImportError:
    print("❌ 需要安装MCP: pip install mcp")
    sys.exit(1)


class MCPClientDemo:
    """MCP客户端演示类"""
    
    def __init__(self):
        """初始化客户端"""
        # 配置服务器参数
        self.server_params = StdioServerParameters(
            command="python",
            args=["demo_mcp_server.py"],
            cwd="."
        )
    
    async def run_demo(self):
        """运行完整演示"""
        print("🎭 iICrawlerMCP客户端演示")
        print("=" * 50)
        
        try:
            async with stdio_client(self.server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    # 初始化连接
                    print("🔗 正在连接MCP服务器...")
                    await session.initialize()
                    print("✅ 连接成功！")
                    print()
                    
                    # 演示1: 列出可用工具
                    await self.demo_list_tools(session)
                    
                    # 演示2: 执行智能网页任务
                    await self.demo_intelligent_web_task(session)
                    
                    # 演示3: 查询浏览器状态
                    await self.demo_browser_status(session)
                    
                    # 演示4: 执行截图
                    await self.demo_take_screenshot(session)
                    
                    # 演示5: 清理资源
                    await self.demo_cleanup_browser(session)
                    
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            logger.exception("演示过程中发生错误")
    
    async def demo_list_tools(self, session: ClientSession):
        """演示1: 列出可用工具"""
        print("📋 演示1: 列出可用工具")
        print("-" * 30)
        
        try:
            tools = await session.list_tools()
            print(f"发现 {len(tools.tools)} 个可用工具:")
            
            for tool in tools.tools:
                print(f"  🔧 {tool.name}")
                print(f"     描述: {tool.description}")
                print()
            
        except Exception as e:
            print(f"❌ 列出工具失败: {e}")
        
        print()
    
    async def demo_intelligent_web_task(self, session: ClientSession):
        """演示2: 执行智能网页任务"""
        print("🤖 演示2: 执行智能网页任务")
        print("-" * 30)
        
        tasks = [
            "打开google.com并截图",
            "搜索'Python MCP'相关内容",
            "获取页面标题和URL"
        ]
        
        for task in tasks:
            try:
                print(f"📝 任务: {task}")
                result = await session.call_tool(
                    "intelligent_web_task",
                    {"task_description": task}
                )
                
                # 提取结果文本
                if result.content and isinstance(result.content[0], TextContent):
                    response = result.content[0].text
                    print(f"✅ 结果: {response}")
                else:
                    print(f"✅ 结果: {result}")
                print()
                
            except Exception as e:
                print(f"❌ 任务执行失败: {e}")
                print()
        
        print()
    
    async def demo_browser_status(self, session: ClientSession):
        """演示3: 查询浏览器状态"""
        print("🌐 演示3: 查询浏览器状态")
        print("-" * 30)
        
        try:
            result = await session.call_tool("browser_status", {})
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"📊 浏览器状态: {response}")
            else:
                print(f"📊 浏览器状态: {result}")
            
        except Exception as e:
            print(f"❌ 状态查询失败: {e}")
        
        print()
        print()
    
    async def demo_take_screenshot(self, session: ClientSession):
        """演示4: 执行截图"""
        print("📸 演示4: 执行截图")
        print("-" * 30)
        
        try:
            result = await session.call_tool("take_screenshot", {})
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"📷 截图结果: {response}")
            else:
                print(f"📷 截图结果: {result}")
            
        except Exception as e:
            print(f"❌ 截图失败: {e}")
        
        print()
        print()
    
    async def demo_cleanup_browser(self, session: ClientSession):
        """演示5: 清理资源"""
        print("🧹 演示5: 清理浏览器资源")
        print("-" * 30)
        
        try:
            result = await session.call_tool("cleanup_browser", {})
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"🗑️ 清理结果: {response}")
            else:
                print(f"🗑️ 清理结果: {result}")
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")
        
        print()
        print("🎉 演示完成！")


async def main():
    """主函数"""
    client = MCPClientDemo()
    await client.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
