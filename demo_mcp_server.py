#!/usr/bin/env python3
"""
iICrawlerMCP演示服务器 - 使用FastMCP
简化版本用于演示MCP接口的使用
"""

import asyncio
import logging
import sys
from typing import Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

try:
    from mcp.server.fastmcp import FastMCP
except ImportError:
    print("❌ 需要安装MCP: pip install mcp", file=sys.stderr)
    sys.exit(1)

# 创建FastMCP服务器
mcp = FastMCP("iICrawlerMCP-Demo")

@mcp.tool()
async def intelligent_web_task(task_description: str) -> str:
    """
    智能网页任务统一入口
    
    Args:
        task_description: 自然语言描述的网页任务
        
    Returns:
        任务执行结果
    """
    logger.info(f"🔧 执行智能网页任务: {task_description}")
    
    # 模拟任务执行
    if "google" in task_description.lower():
        result = "✅ 已打开Google首页并截图保存"
    elif "截图" in task_description:
        result = "✅ 已保存当前页面截图到screenshots/目录"
    elif "搜索" in task_description:
        result = "✅ 已执行搜索操作并获取结果"
    else:
        result = f"✅ 已执行任务: {task_description}"
    
    logger.info(f"✅ 任务完成: {result}")
    return result

@mcp.tool()
async def browser_status() -> str:
    """
    查询浏览器状态
    
    Returns:
        浏览器状态信息
    """
    logger.info("🔧 查询浏览器状态")
    
    status = {
        "url": "https://www.google.com",
        "title": "Google",
        "status": "ready",
        "viewport": "1920x1080"
    }
    
    result = f"浏览器状态: {status}"
    logger.info(f"✅ 状态查询完成: {result}")
    return result

@mcp.tool()
async def take_screenshot() -> str:
    """
    快速截图
    
    Returns:
        截图结果
    """
    logger.info("🔧 执行截图")
    
    result = "✅ 截图已保存到: screenshots/screenshot_2025-07-29_21-50-00.png"
    logger.info(f"✅ 截图完成: {result}")
    return result

@mcp.tool()
async def cleanup_browser() -> str:
    """
    清理浏览器资源
    
    Returns:
        清理结果
    """
    logger.info("🔧 清理浏览器资源")
    
    result = "✅ 浏览器资源已清理，内存已释放"
    logger.info(f"✅ 清理完成: {result}")
    return result

def main():
    """主函数"""
    print("🚀 启动iICrawlerMCP演示服务器...", file=sys.stderr)
    print("📡 服务器名称: iICrawlerMCP-Demo", file=sys.stderr)
    print("📡 MCP协议: stdio", file=sys.stderr)
    print("🤖 主Agent: CrawlerAgent", file=sys.stderr)
    print("🔧 可用工具:", file=sys.stderr)
    print("   - intelligent_web_task: 智能网页任务统一入口", file=sys.stderr)
    print("   - browser_status: 浏览器状态查询", file=sys.stderr)
    print("   - take_screenshot: 快速截图", file=sys.stderr)
    print("   - cleanup_browser: 资源清理", file=sys.stderr)
    print("-" * 50, file=sys.stderr)
    print("💡 提示: 使用Ctrl+C停止服务器", file=sys.stderr)
    print("-" * 50, file=sys.stderr)

    try:
        # 运行FastMCP服务器 - 不使用asyncio.run，让FastMCP自己管理事件循环
        mcp.run(transport="stdio")
    except KeyboardInterrupt:
        print("\n👋 服务器已停止", file=sys.stderr)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}", file=sys.stderr)
        raise

if __name__ == "__main__":
    main()
