#!/usr/bin/env python3
"""
直接测试iICrawlerMCP功能
不通过MCP协议，直接调用功能来验证是否工作
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 环境变量加载成功")
except ImportError:
    print("⚠️ python-dotenv未安装，跳过.env文件加载")

def test_direct_functionality():
    """直接测试iICrawlerMCP功能"""
    print("🎭 直接测试iICrawlerMCP功能")
    print("=" * 50)
    
    try:
        # 导入iICrawlerMCP
        print("📦 导入iICrawlerMCP模块...")
        from iicrawlermcp.agents import build_agent
        from iicrawlermcp.core.config import config
        print("✅ 模块导入成功")
        
        # 验证配置
        print("🔍 验证配置...")
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建agent
        print("🤖 创建CrawlerAgent...")
        agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 测试简单任务
        print("🔧 测试任务: 打开google.com")
        print("⚡ 正在执行... (这将实际打开浏览器)")
        
        result = agent.invoke({"input": "打开google.com"})
        
        print(f"✅ 任务执行完成")
        print(f"📋 结果类型: {type(result)}")
        
        if isinstance(result, dict):
            output = result.get('output', str(result))
            print(f"📄 输出: {output}")
        else:
            print(f"📄 输出: {result}")
        
        # 等待用户查看
        input("\n⏳ 按Enter继续下一个测试...")
        
        # 测试状态查询
        print("\n🔧 测试任务: 获取页面信息")
        result2 = agent.invoke({"input": "获取当前页面的URL和标题"})
        
        if isinstance(result2, dict):
            output2 = result2.get('output', str(result2))
            print(f"📄 页面信息: {output2}")
        else:
            print(f"📄 页面信息: {result2}")
        
        # 等待用户查看
        input("\n⏳ 按Enter继续截图测试...")
        
        # 测试截图
        print("\n🔧 测试任务: 截图")
        result3 = agent.invoke({"input": "对当前页面进行截图"})
        
        if isinstance(result3, dict):
            output3 = result3.get('output', str(result3))
            print(f"📷 截图结果: {output3}")
        else:
            print(f"📷 截图结果: {result3}")
        
        # 清理
        print("\n🧹 清理资源...")
        result4 = agent.invoke({"input": "关闭浏览器"})
        
        if isinstance(result4, dict):
            output4 = result4.get('output', str(result4))
            print(f"🗑️ 清理结果: {output4}")
        else:
            print(f"🗑️ 清理结果: {result4}")
        
        print("\n🎉 直接测试完成！")
        print("💡 如果您看到了浏览器操作，说明iICrawlerMCP功能正常")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_direct_functionality()
