#!/usr/bin/env python3
"""
iICrawlerMCP交互式客户端
允许用户通过命令行与MCP服务器实时交互
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List

# 设置日志
logging.basicConfig(
    level=logging.WARNING,  # 减少日志输出，保持界面清洁
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    from mcp.types import TextContent
except ImportError:
    print("❌ 需要安装MCP: pip install mcp")
    sys.exit(1)


class InteractiveMCPClient:
    """交互式MCP客户端"""
    
    def __init__(self):
        """初始化客户端"""
        self.server_params = StdioServerParameters(
            command="python",
            args=["demo_mcp_server.py"],
            cwd="."
        )
        self.session = None
        self.available_tools = []
    
    async def start(self):
        """启动交互式客户端"""
        print("🎭 iICrawlerMCP交互式客户端")
        print("=" * 50)
        print("正在连接MCP服务器...")
        
        try:
            async with stdio_client(self.server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    self.session = session
                    
                    # 初始化连接
                    await session.initialize()
                    print("✅ 连接成功！")
                    
                    # 获取可用工具
                    await self.load_tools()
                    
                    # 显示欢迎信息
                    self.show_welcome()
                    
                    # 开始交互循环
                    await self.interactive_loop()
                    
        except KeyboardInterrupt:
            print("\n👋 再见！")
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            logger.exception("连接过程中发生错误")
    
    async def load_tools(self):
        """加载可用工具列表"""
        try:
            tools = await self.session.list_tools()
            self.available_tools = tools.tools
            print(f"发现 {len(self.available_tools)} 个可用工具")
        except Exception as e:
            print(f"❌ 加载工具失败: {e}")
    
    def show_welcome(self):
        """显示欢迎信息和帮助"""
        print()
        print("🔧 可用工具:")
        for i, tool in enumerate(self.available_tools, 1):
            print(f"  {i}. {tool.name} - {tool.description.split('.')[0] if tool.description else '无描述'}")
        
        print()
        print("💡 使用方法:")
        print("  - 输入工具编号 (1-4) 来快速调用工具")
        print("  - 输入 'task: 你的任务描述' 来执行智能网页任务")
        print("  - 输入 'help' 查看帮助")
        print("  - 输入 'quit' 或 Ctrl+C 退出")
        print("=" * 50)
    
    async def interactive_loop(self):
        """交互循环"""
        while True:
            try:
                # 获取用户输入
                user_input = input("\n🤖 请输入命令: ").strip()
                
                if not user_input:
                    continue
                
                # 处理退出命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                # 处理帮助命令
                if user_input.lower() in ['help', 'h', '?']:
                    self.show_help()
                    continue
                
                # 处理任务命令
                if user_input.lower().startswith('task:'):
                    task_description = user_input[5:].strip()
                    if task_description:
                        await self.execute_intelligent_task(task_description)
                    else:
                        print("❌ 请提供任务描述，例如: task: 打开google.com")
                    continue
                
                # 处理工具编号
                if user_input.isdigit():
                    tool_index = int(user_input) - 1
                    if 0 <= tool_index < len(self.available_tools):
                        await self.execute_tool_by_index(tool_index)
                    else:
                        print(f"❌ 无效的工具编号，请输入 1-{len(self.available_tools)}")
                    continue
                
                # 处理直接工具名称
                tool_found = False
                for tool in self.available_tools:
                    if tool.name.lower() == user_input.lower():
                        await self.execute_tool(tool.name, {})
                        tool_found = True
                        break
                
                if not tool_found:
                    print("❌ 未知命令，输入 'help' 查看帮助")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 执行失败: {e}")
                logger.exception("执行过程中发生错误")
    
    def show_help(self):
        """显示帮助信息"""
        print("\n📖 帮助信息")
        print("-" * 30)
        print("可用命令:")
        print("  1-4          - 快速调用对应编号的工具")
        print("  task: 描述   - 执行智能网页任务")
        print("  help         - 显示此帮助信息")
        print("  quit         - 退出程序")
        print()
        print("工具列表:")
        for i, tool in enumerate(self.available_tools, 1):
            print(f"  {i}. {tool.name}")
        print()
        print("示例:")
        print("  task: 打开google.com并截图")
        print("  task: 搜索Python相关内容")
        print("  1  (调用intelligent_web_task)")
        print("  2  (调用browser_status)")
    
    async def execute_intelligent_task(self, task_description: str):
        """执行智能网页任务"""
        print(f"🔧 执行任务: {task_description}")
        await self.execute_tool("intelligent_web_task", {"task_description": task_description})
    
    async def execute_tool_by_index(self, index: int):
        """根据索引执行工具"""
        tool = self.available_tools[index]
        
        if tool.name == "intelligent_web_task":
            # 对于智能任务，需要用户输入描述
            task_description = input("📝 请输入任务描述: ").strip()
            if task_description:
                await self.execute_tool(tool.name, {"task_description": task_description})
            else:
                print("❌ 任务描述不能为空")
        else:
            # 其他工具直接执行
            await self.execute_tool(tool.name, {})
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """执行工具"""
        try:
            print(f"⚡ 正在执行 {tool_name}...")
            
            result = await self.session.call_tool(tool_name, arguments)
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"✅ 结果: {response}")
            else:
                print(f"✅ 结果: {result}")
                
        except Exception as e:
            print(f"❌ 执行失败: {e}")


async def main():
    """主函数"""
    client = InteractiveMCPClient()
    await client.start()


if __name__ == "__main__":
    asyncio.run(main())
