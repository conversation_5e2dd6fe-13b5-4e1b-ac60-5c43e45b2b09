#!/usr/bin/env python3
"""
调用iICrawlerMCP的客户端
"""

import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def call_iicrawler():
    # 连接到MCP服务器
    server_params = StdioServerParameters(
        command="python",
        args=["real_mcp_server.py"],  # 或者 scripts/start_mcp_server.py
        cwd="."
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            print("✅ 连接MCP服务器成功")
            
            # 调用智能网页任务
            result = await session.call_tool(
                "intelligent_web_task",
                {"task_description": "打开google.com并搜索Python"}
            )
            
            print(f"结果: {result.content[0].text}")

if __name__ == "__main__":
    asyncio.run(call_iicrawler())
