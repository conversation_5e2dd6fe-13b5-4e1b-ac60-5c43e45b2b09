#!/usr/bin/env python3
"""
您的自定义MCP客户端示例
"""

import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.types import TextContent

async def my_web_automation():
    """自定义网页自动化任务"""
    
    # 配置服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["demo_mcp_server.py"],
        cwd="."
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # 初始化连接
            await session.initialize()
            print("✅ 连接MCP服务器成功！")
            
            # 示例1: 打开网站
            print("\n🌐 打开百度首页...")
            result = await session.call_tool(
                "intelligent_web_task",
                {"task_description": "打开baidu.com"}
            )
            print(f"结果: {result.content[0].text}")
            
            # 示例2: 搜索内容
            print("\n🔍 搜索内容...")
            result = await session.call_tool(
                "intelligent_web_task", 
                {"task_description": "在搜索框中输入'Python编程'并搜索"}
            )
            print(f"结果: {result.content[0].text}")
            
            # 示例3: 截图
            print("\n📸 截图...")
            result = await session.call_tool("take_screenshot", {})
            print(f"结果: {result.content[0].text}")
            
            # 示例4: 查看状态
            print("\n📊 查看浏览器状态...")
            result = await session.call_tool("browser_status", {})
            print(f"结果: {result.content[0].text}")
            
            # 示例5: 清理
            print("\n🧹 清理资源...")
            result = await session.call_tool("cleanup_browser", {})
            print(f"结果: {result.content[0].text}")

if __name__ == "__main__":
    asyncio.run(my_web_automation())
