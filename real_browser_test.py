#!/usr/bin/env python3
"""
真实浏览器测试 - 这会真正打开浏览器
"""

import asyncio
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 环境变量加载成功")
except ImportError:
    print("⚠️ python-dotenv未安装，跳过.env文件加载")

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    from mcp.types import TextContent
except ImportError:
    print("❌ 需要安装MCP: pip install mcp")
    sys.exit(1)


async def real_browser_demo():
    """真实浏览器演示 - 会真正打开浏览器"""
    print("🎭 真实浏览器演示")
    print("=" * 50)
    print("⚠️  注意: 这将真正打开浏览器窗口！")
    print("=" * 50)
    
    # 配置服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["real_mcp_server.py"],
        cwd="."
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化连接
                print("🔗 正在连接真实MCP服务器...")
                await session.initialize()
                print("✅ 连接成功！")
                print()
                
                # 演示1: 打开Google - 这会真正打开浏览器
                print("🌐 演示1: 真正打开Google")
                print("-" * 30)
                print("📝 任务: 打开google.com")
                print("⚡ 正在执行... (您应该会看到浏览器窗口打开)")
                
                result = await session.call_tool(
                    "intelligent_web_task",
                    {"task_description": "打开google.com"}
                )
                
                if result.content and isinstance(result.content[0], TextContent):
                    response = result.content[0].text
                    print(f"✅ 结果: {response}")
                else:
                    print(f"✅ 结果: {result}")
                
                # 等待用户确认看到浏览器
                input("\n⏳ 您看到浏览器窗口了吗？按Enter继续...")
                
                # 演示2: 搜索 - 真实搜索
                print("\n🔍 演示2: 真实搜索")
                print("-" * 30)
                search_query = "Python MCP Model Context Protocol"
                print(f"📝 任务: 搜索 '{search_query}'")
                print("⚡ 正在执行真实搜索...")
                
                result = await session.call_tool(
                    "intelligent_web_task",
                    {"task_description": f"在Google搜索框中输入'{search_query}'并点击搜索"}
                )
                
                if result.content and isinstance(result.content[0], TextContent):
                    response = result.content[0].text
                    print(f"✅ 搜索结果: {response}")
                else:
                    print(f"✅ 搜索结果: {result}")
                
                # 等待搜索完成
                input("\n⏳ 您看到搜索结果了吗？按Enter继续...")
                
                # 演示3: 获取页面信息
                print("\n📋 演示3: 获取真实页面信息")
                print("-" * 30)
                print("📝 任务: 获取当前页面的URL和标题")
                
                result = await session.call_tool(
                    "intelligent_web_task",
                    {"task_description": "获取当前页面的URL和标题"}
                )
                
                if result.content and isinstance(result.content[0], TextContent):
                    response = result.content[0].text
                    print(f"📄 页面信息: {response}")
                else:
                    print(f"📄 页面信息: {result}")
                
                # 演示4: 截图
                print("\n📸 演示4: 真实截图")
                print("-" * 30)
                print("📷 正在对当前页面截图...")
                
                result = await session.call_tool("take_screenshot", {})
                
                if result.content and isinstance(result.content[0], TextContent):
                    response = result.content[0].text
                    print(f"📁 截图结果: {response}")
                else:
                    print(f"📁 截图结果: {result}")
                
                # 演示5: 清理
                print("\n🧹 演示5: 清理浏览器")
                print("-" * 30)
                print("🗑️ 正在关闭浏览器...")
                
                result = await session.call_tool("cleanup_browser", {})
                
                if result.content and isinstance(result.content[0], TextContent):
                    response = result.content[0].text
                    print(f"✅ 清理结果: {response}")
                else:
                    print(f"✅ 清理结果: {result}")
                
                print("\n🎉 真实演示完成！")
                print("💡 您应该已经看到了真实的浏览器操作过程")
                
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("⚠️  准备开始真实浏览器演示")
    print("这将真正打开浏览器并执行网页操作")
    
    # 给用户一些时间准备
    for i in range(3, 0, -1):
        print(f"⏳ {i}秒后开始...")
        await asyncio.sleep(1)
    
    await real_browser_demo()


if __name__ == "__main__":
    asyncio.run(main())
