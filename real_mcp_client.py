#!/usr/bin/env python3
"""
真实的iICrawlerMCP客户端演示
实际调用iICrawlerMCP功能，展示真实的网页自动化
"""

import asyncio
import sys
import time

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    from mcp.types import TextContent
except ImportError:
    print("❌ 需要安装MCP: pip install mcp")
    sys.exit(1)


class RealMCPClientDemo:
    """真实MCP客户端演示类"""
    
    def __init__(self):
        """初始化客户端"""
        # 配置服务器参数
        self.server_params = StdioServerParameters(
            command="python",
            args=["real_mcp_server.py"],
            cwd="."
        )
    
    async def run_real_demo(self):
        """运行真实演示"""
        print("🎭 iICrawlerMCP真实功能演示")
        print("=" * 50)
        print("⚠️  注意: 这将实际打开浏览器并执行网页操作")
        print("=" * 50)
        
        try:
            async with stdio_client(self.server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    # 初始化连接
                    print("🔗 正在连接真实MCP服务器...")
                    await session.initialize()
                    print("✅ 连接成功！")
                    print()
                    
                    # 演示1: 打开Google并截图
                    await self.demo_open_google(session)
                    
                    # 演示2: 查询浏览器状态
                    await self.demo_browser_status(session)
                    
                    # 演示3: 执行搜索
                    await self.demo_search(session)
                    
                    # 演示4: 截图
                    await self.demo_screenshot(session)
                    
                    # 演示5: 清理资源
                    await self.demo_cleanup(session)
                    
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def demo_open_google(self, session: ClientSession):
        """演示1: 打开Google"""
        print("🌐 演示1: 打开Google首页")
        print("-" * 30)
        
        try:
            print("📝 任务: 打开google.com")
            print("⚡ 正在执行... (这将实际打开浏览器)")
            
            result = await session.call_tool(
                "intelligent_web_task",
                {"task_description": "打开google.com"}
            )
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"✅ 结果: {response}")
            else:
                print(f"✅ 结果: {result}")
            
            # 等待一下让用户看到浏览器
            print("⏳ 等待3秒让您看到浏览器...")
            await asyncio.sleep(3)
            
        except Exception as e:
            print(f"❌ 打开Google失败: {e}")
        
        print()
    
    async def demo_browser_status(self, session: ClientSession):
        """演示2: 查询浏览器状态"""
        print("📊 演示2: 查询浏览器状态")
        print("-" * 30)
        
        try:
            print("🔍 正在查询当前页面状态...")
            
            result = await session.call_tool("browser_status", {})
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"📋 状态信息: {response}")
            else:
                print(f"📋 状态信息: {result}")
            
        except Exception as e:
            print(f"❌ 状态查询失败: {e}")
        
        print()
    
    async def demo_search(self, session: ClientSession):
        """演示3: 执行搜索"""
        print("🔍 演示3: 执行搜索")
        print("-" * 30)
        
        try:
            search_query = "Python MCP Model Context Protocol"
            print(f"📝 任务: 在Google搜索 '{search_query}'")
            print("⚡ 正在执行搜索...")
            
            result = await session.call_tool(
                "intelligent_web_task",
                {"task_description": f"在Google搜索框中输入'{search_query}'并点击搜索"}
            )
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"✅ 搜索结果: {response}")
            else:
                print(f"✅ 搜索结果: {result}")
            
            # 等待搜索结果加载
            print("⏳ 等待3秒让搜索结果加载...")
            await asyncio.sleep(3)
            
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
        
        print()
    
    async def demo_screenshot(self, session: ClientSession):
        """演示4: 截图"""
        print("📸 演示4: 截图当前页面")
        print("-" * 30)
        
        try:
            print("📷 正在截图当前页面...")
            
            result = await session.call_tool("take_screenshot", {})
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"📁 截图结果: {response}")
            else:
                print(f"📁 截图结果: {result}")
            
        except Exception as e:
            print(f"❌ 截图失败: {e}")
        
        print()
    
    async def demo_cleanup(self, session: ClientSession):
        """演示5: 清理资源"""
        print("🧹 演示5: 清理浏览器资源")
        print("-" * 30)
        
        try:
            print("🗑️ 正在关闭浏览器并清理资源...")
            
            result = await session.call_tool("cleanup_browser", {})
            
            if result.content and isinstance(result.content[0], TextContent):
                response = result.content[0].text
                print(f"✅ 清理结果: {response}")
            else:
                print(f"✅ 清理结果: {result}")
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")
        
        print()
        print("🎉 真实演示完成！")
        print("💡 您应该已经看到了实际的浏览器操作过程")


async def main():
    """主函数"""
    print("⚠️  准备开始真实演示")
    print("这将实际打开浏览器并执行网页操作")
    
    # 给用户一些时间准备
    for i in range(3, 0, -1):
        print(f"⏳ {i}秒后开始...")
        await asyncio.sleep(1)
    
    client = RealMCPClientDemo()
    await client.run_real_demo()


if __name__ == "__main__":
    asyncio.run(main())
