#!/usr/bin/env python3
"""
真实的iICrawlerMCP服务器 - 使用FastMCP
实际调用iICrawlerMCP的功能，而不是模拟结果
"""

import asyncio
import logging
import sys
import os
from typing import Optional

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 环境变量加载成功", file=sys.stderr)
except ImportError:
    print("⚠️ python-dotenv未安装，跳过.env文件加载", file=sys.stderr)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

try:
    from mcp.server.fastmcp import FastMCP
except ImportError:
    print("❌ 需要安装MCP: pip install mcp", file=sys.stderr)
    sys.exit(1)

# 导入iICrawlerMCP功能
try:
    from iicrawlermcp.agents import build_agent
    from iicrawlermcp.core.config import config
    print("✅ iICrawlerMCP模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"❌ iICrawlerMCP模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

# 创建FastMCP服务器
mcp = FastMCP("iICrawlerMCP-Real")

# 全局agent实例
crawler_agent = None

async def get_crawler_agent():
    """获取或创建CrawlerAgent实例"""
    global crawler_agent
    if crawler_agent is None:
        try:
            logger.info("🤖 初始化CrawlerAgent...")
            crawler_agent = build_agent()
            logger.info("✅ CrawlerAgent初始化完成")
        except Exception as e:
            logger.error(f"❌ CrawlerAgent初始化失败: {e}")
            raise
    return crawler_agent

@mcp.tool()
async def intelligent_web_task(task_description: str) -> str:
    """
    智能网页任务统一入口 - 真实实现
    
    Args:
        task_description: 自然语言描述的网页任务
        
    Returns:
        任务执行结果
    """
    logger.info(f"🔧 执行真实智能网页任务: {task_description}")
    
    try:
        # 获取CrawlerAgent
        agent = await get_crawler_agent()
        
        # 执行任务
        logger.info("⚡ 正在执行任务...")
        result = agent.invoke({"input": task_description})
        
        # 提取结果
        if isinstance(result, dict):
            output = result.get('output', str(result))
        else:
            output = str(result)
        
        logger.info(f"✅ 任务执行完成")
        return output
        
    except Exception as e:
        error_msg = f"❌ 任务执行失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.tool()
async def browser_status() -> str:
    """
    查询真实浏览器状态
    
    Returns:
        浏览器状态信息
    """
    logger.info("🔧 查询真实浏览器状态")
    
    try:
        # 获取CrawlerAgent
        agent = await get_crawler_agent()
        
        # 执行状态查询
        result = agent.invoke({"input": "获取当前页面的URL和标题"})
        
        if isinstance(result, dict):
            output = result.get('output', str(result))
        else:
            output = str(result)
        
        logger.info(f"✅ 状态查询完成")
        return f"浏览器状态: {output}"
        
    except Exception as e:
        error_msg = f"❌ 状态查询失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.tool()
async def take_screenshot() -> str:
    """
    真实截图功能
    
    Returns:
        截图结果
    """
    logger.info("🔧 执行真实截图")
    
    try:
        # 获取CrawlerAgent
        agent = await get_crawler_agent()
        
        # 执行截图
        result = agent.invoke({"input": "对当前页面进行截图"})
        
        if isinstance(result, dict):
            output = result.get('output', str(result))
        else:
            output = str(result)
        
        logger.info(f"✅ 截图完成")
        return output
        
    except Exception as e:
        error_msg = f"❌ 截图失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.tool()
async def cleanup_browser() -> str:
    """
    真实清理浏览器资源
    
    Returns:
        清理结果
    """
    logger.info("🔧 清理真实浏览器资源")
    
    try:
        # 获取CrawlerAgent
        agent = await get_crawler_agent()
        
        # 执行清理
        result = agent.invoke({"input": "关闭浏览器并清理资源"})
        
        if isinstance(result, dict):
            output = result.get('output', str(result))
        else:
            output = str(result)
        
        # 重置agent实例
        global crawler_agent
        crawler_agent = None
        
        logger.info(f"✅ 清理完成")
        return output
        
    except Exception as e:
        error_msg = f"❌ 清理失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

def main():
    """主函数"""
    print("🚀 启动真实iICrawlerMCP服务器...", file=sys.stderr)
    print("📡 服务器名称: iICrawlerMCP-Real", file=sys.stderr)
    print("📡 MCP协议: stdio", file=sys.stderr)
    print("🤖 主Agent: CrawlerAgent (真实实现)", file=sys.stderr)
    print("🔧 可用工具:", file=sys.stderr)
    print("   - intelligent_web_task: 智能网页任务统一入口 (真实)", file=sys.stderr)
    print("   - browser_status: 浏览器状态查询 (真实)", file=sys.stderr)
    print("   - take_screenshot: 快速截图 (真实)", file=sys.stderr)
    print("   - cleanup_browser: 资源清理 (真实)", file=sys.stderr)
    print("-" * 50, file=sys.stderr)
    print("💡 提示: 使用Ctrl+C停止服务器", file=sys.stderr)
    print("-" * 50, file=sys.stderr)
    
    try:
        # 验证配置
        print("🔍 验证配置...", file=sys.stderr)
        config.validate()
        print("✅ 配置验证通过", file=sys.stderr)
        
        # 运行FastMCP服务器
        mcp.run(transport="stdio")
    except KeyboardInterrupt:
        print("\n👋 服务器已停止", file=sys.stderr)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}", file=sys.stderr)
        raise

if __name__ == "__main__":
    main()
