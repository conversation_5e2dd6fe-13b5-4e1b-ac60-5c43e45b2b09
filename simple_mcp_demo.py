#!/usr/bin/env python3
"""
简单的MCP演示脚本
展示如何使用iICrawlerMCP的MCP接口
"""

import asyncio
import sys

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    from mcp.types import TextContent
except ImportError:
    print("❌ 需要安装MCP: pip install mcp")
    sys.exit(1)


async def simple_demo():
    """简单演示"""
    print("🎭 iICrawlerMCP简单演示")
    print("=" * 40)
    
    # 配置服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["demo_mcp_server.py"],
        cwd="."
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化连接
                print("🔗 连接MCP服务器...")
                await session.initialize()
                print("✅ 连接成功！\n")
                
                # 演示各种任务
                tasks = [
                    ("打开Google首页", "intelligent_web_task", {"task_description": "打开google.com"}),
                    ("查询浏览器状态", "browser_status", {}),
                    ("执行截图", "take_screenshot", {}),
                    ("搜索内容", "intelligent_web_task", {"task_description": "搜索Python MCP相关内容"}),
                    ("清理资源", "cleanup_browser", {})
                ]
                
                for i, (desc, tool_name, args) in enumerate(tasks, 1):
                    print(f"📝 演示 {i}: {desc}")
                    print(f"🔧 调用工具: {tool_name}")
                    
                    try:
                        result = await session.call_tool(tool_name, args)
                        
                        if result.content and isinstance(result.content[0], TextContent):
                            response = result.content[0].text
                            print(f"✅ 结果: {response}")
                        else:
                            print(f"✅ 结果: {result}")
                            
                    except Exception as e:
                        print(f"❌ 失败: {e}")
                    
                    print("-" * 40)
                    
                    # 添加延迟让用户看清楚
                    await asyncio.sleep(1)
                
                print("🎉 演示完成！")
                
    except Exception as e:
        print(f"❌ 演示失败: {e}")


if __name__ == "__main__":
    asyncio.run(simple_demo())
